0 verbose cli C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\bin\npm-cli.js
1 info using npm@10.9.2
2 info using node@v22.13.0
3 silly config load:file:C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\npmrc
4 silly config load:file:C:\Users\<USER>\Desktop\Stonehenge Investments\pumpkin-main\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:C:\Users\<USER>\AppData\Roaming\npm\etc\npmrc
7 verbose title npm run lint
8 verbose argv "run" "lint" "--cache" "--fix"
9 verbose logfile logs-max:10 dir:C:\Users\<USER>\Desktop\Stonehenge Investments\pumpkin-main\--fix\_logs\2025-05-18T15_33_23_947Z-
10 verbose logfile C:\Users\<USER>\Desktop\Stonehenge Investments\pumpkin-main\--fix\_logs\2025-05-18T15_33_23_947Z-debug-0.log
11 silly logfile done cleaning log files
12 http fetch GET 200 https://registry.npmjs.org/npm 3003ms
13 verbose cwd C:\Users\<USER>\Desktop\Stonehenge Investments\pumpkin-main
14 verbose os Windows_NT 10.0.19045
15 verbose node v22.13.0
16 verbose npm  v10.9.2
17 notice
17 notice New [31mmajor[39m version of npm available! [31m10.9.2[39m -> [34m11.4.0[39m
17 notice Changelog: [34mhttps://github.com/npm/cli/releases/tag/v11.4.0[39m
17 notice To update run: [4mnpm install -g npm@11.4.0[24m
17 notice  { force: true, [Symbol(proc-log.meta)]: true }
18 verbose exit 1
19 verbose code 1

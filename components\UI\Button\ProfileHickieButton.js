"use client";

import React, { useEffect, useState } from "react";
import { useCookies } from "next-client-cookies";
import { FaHeart } from "react-icons/fa";
import { BiHeart } from "react-icons/bi";

export default function ProfileHickieButton({ targetEmail, initialHickies, initialHickiedBy, onHickieUpdate }) {
  const cookies = useCookies();
  const currentUserEmail = cookies.get("email");

  const [hickied, setHickied] = useState(false);
  const [hickieCount, setHickieCount] = useState(initialHickies || 0);
  const [hickiedBy, setHickiedBy] = useState(initialHickiedBy || []);

  // Sync internal state with props when initialHickies/initialHickiedBy or targetEmail changes
  useEffect(() => {
    // console.log("PHB useEffect: Re-sync for", targetEmail);
    // console.log("  initialHickies:", initialHickies, "initialHickiedBy:", initialHickiedBy);
    setHickieCount(initialHickies || 0);
    setHickiedBy(initialHickiedBy || []);
    setHickied((initialHickiedBy || []).includes(currentUserEmail));
    // console.log("  PHB useEffect: Set hickied to", (initialHickiedBy || []).includes(currentUserEmail));
  }, [initialHickies, initialHickiedBy, currentUserEmail, targetEmail]);


  const toggleHickie = async () => {
    if (!currentUserEmail) {
        alert("Please log in to leave a Hickie.");
        return;
    }

    const isCurrentlyHickied = hickied; // Store current state before optimistic update

    // Calculate new state values
    let newHickiedBy;
    let newHickieCount;

    if (isCurrentlyHickied) {
      // User is un-hickieing
      newHickiedBy = hickiedBy.filter(email => email !== currentUserEmail);
      newHickieCount = hickieCount - 1;
    } else {
      // User is hickieing
      newHickiedBy = [...hickiedBy, currentUserEmail];
      newHickieCount = hickieCount + 1;
    }

    // Optimistically update local state (UI responds immediately)
    setHickied(!isCurrentlyHickied);
    setHickiedBy(newHickiedBy);
    setHickieCount(newHickieCount);
    // console.log("PHB: Optimistic update. newHickied:", !isCurrentlyHickied, "newHickieCount:", newHickieCount);

    try {
      const res = await fetch("/api/profile-hickie", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          targetEmail,
          hickiedBy: newHickiedBy,
          hickies: newHickieCount,
        }),
      });
    
       const result = await res.json();

      if (!res.ok || !result.success) {
        // If API call failed, revert local state
        console.error("PHB: API request failed. Reverting optimistic UI. Status:");
        setHickied(isCurrentlyHickied);
        setHickiedBy(hickiedBy); // Revert to original hickiedBy
        setHickieCount(hickieCount); // Revert to original hickieCount
        alert("Failed to update Hickie. Please try again."); // Provide user feedback
      } else {
        // If API call succeeded, confirm the update to parent (MatchesHome)
        // Parent will update its 'users' array, which will then flow back as new props
        // to this component, solidifying the change.
        // console.log("PHB: API request successful. Calling onHickieUpdate.");
        if (onHickieUpdate) {
            onHickieUpdate(targetEmail, newHickieCount, newHickiedBy);
        }
      }

    } catch (error) {
      // Catch any network errors or errors thrown by 'res.json()' if not handled above
      console.error("PHB: Network or unexpected error during toggleHickie. Reverting UI. Error:", error);
      setHickied(isCurrentlyHickied);
      setHickiedBy(hickiedBy); // Revert to original hickiedBy
      setHickieCount(hickieCount); // Revert to original hickieCount
      alert("An unexpected error occurred. Please try again."); // Provide user feedback
    }
  };

  return (
    <div onClick={toggleHickie} className="cursor-pointer active:scale-105
    w-20 h-20 rounded-full flex items-center justify-center flex-shrink-0
    bg-white hover:bg-gray-100 text-black border-2 border-gray-300 shadow-md
    transition-all duration-300 ease-in-out">
      {hickied ? <FaHeart className="text-red-500 h-10 w-10" /> : <BiHeart className="h-8 w-8" />}
    </div>
  );
}

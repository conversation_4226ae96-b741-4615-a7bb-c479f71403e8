const { NextResponse } = require('next/server');
const { MongoClient, ObjectId } = require('mongodb');
const { default: clientPromise } = require('@/lib/mongodb');

// Helper to get database instance
async function getDb() {
  const client = await clientPromise;
  return client.db('Pumpkin');
}

export async function POST(req) {
  const db = await getDb();
  const body = await req.json();
  const { senderEmail, senderData, email: receiverEmail, timestamp, seen } = body;

  if (!senderEmail || !receiverEmail || !senderData || !timestamp) {
    return NextResponse.json({ message: 'Missing required fields for POST request' }, { status: 400 });
  }

  try {
    // Check if a pending or accepted request already exists to prevent duplicates
    const existingRequest = await db.collection('PumpkinRequests').findOne({
      $or: [
        { senderEmail: senderEmail, receiverEmail: receiverEmail, status: { $in: ["pending", "accepted"] } },
        { senderEmail: receiverEmail, receiverEmail: senderEmail, status: { $in: ["pending", "accepted"] } }
      ]
    });

    if (existingRequest) {
      return NextResponse.json({ message: 'A pump request already exists or is active between these users.' }, { status: 409 });
    }

    // Insert the new pumpkin request
    const candidateData = await db.collection('PumpkinRequests').insertOne({
      senderEmail,
      senderData,
      receiverEmail,
      timestamp,
      status: "pending", // Initial status
      seen: false, // Ensure seen is initially false
      createdAt: new Date(), // Add creation timestamp
      updatedAt: new Date(), // Add update timestamp
    });
 await db.collection('Notifications').insertOne({
      type: "match_request",
      senderEmail,
      senderData,
      receiverEmail,
      timestamp: new Date().toISOString(),
      seen: false,
      link: `/profile/${senderEmail}`, // Adjust this to your actual profile route
      message: `${senderData.name} sent you a match request!`,
      createdAt: new Date(),
    });
    // Increment sender's pumpkin count in Users collection
    await db.collection("Users").updateOne(
      { email: senderEmail },
      { $inc: { pumpkins: 1 } } // Increment pumpkins by 1
    );

    console.log("Pump request sent successfully:", candidateData.insertedId);
    return NextResponse.json({ acknowledged: candidateData.acknowledged, insertedId: candidateData.insertedId }, { status: 200 });

  } catch (error) {
    console.error('Error sending pump request:', error);
    return NextResponse.json({ message: 'Internal server error', error: error.message }, { status: 500 });
  }
}

export async function GET() {
  const db = await getDb();
  try {
    const userData = await db
      .collection('PumpkinRequests')
      .find({})
      .sort({ createdAt: -1 }) // Sort by latest
      .toArray();
    return NextResponse.json(userData);
  } catch (error) {
    console.error('Error fetching PumpkinRequests:', error);
    return NextResponse.json('error', { status: 500 });
  }
}

export async function PUT(req) {
  const db = await getDb();
  const body = await req.json();
  const { _id, status: newStatus } = body; // newStatus can be 'accepted', 'rejected', 'unmatched', 'pending' (for change mind)

  if (!_id || !newStatus) {
    return NextResponse.json({ message: 'Missing required fields (_id, status)' }, { status: 400 });
  }

  try {
    const objectId = new ObjectId(_id);
    const pumpRequest = await db.collection("PumpkinRequests").findOne({ _id: objectId });

    if (!pumpRequest) {
      return NextResponse.json({ message: 'Pumpkin request not found' }, { status: 404 });
    }

    const { senderEmail, receiverEmail, status: currentStatus } = pumpRequest;

    // Handle pumpkin count increments/decrements based on status change
    if (newStatus === "accepted" && currentStatus === "pending") {
      // If a pending pump is accepted, increment pumpkins for the receiver
      await db.collection("Users").updateOne(
        { email: receiverEmail },
        { $inc: { pumpkins: 1 } }
      );
    } else if (newStatus === "unmatched" && currentStatus === "accepted") {
      // If an accepted pump is unmatched, decrement pumpkins for both
      await db.collection("Users").updateOne(
        { email: senderEmail },
        { $inc: { pumpkins: -1 } }
      );
      await db.collection("Users").updateOne(
        { email: receiverEmail },
        { $inc: { pumpkins: -1 } }
      );
    } else if (newStatus === "pending" && currentStatus === "rejected") { // Change mind scenario
       // If a rejected pump reverts to pending, it's like a 're-pump', decrement original sender's (who was rejected)
       // This logic assumes "change mind" means the receiver wants to reconsider a *rejected* pump.
       // The original PUMP action increased the sender's count. Reverting to pending might reduce sender's count if it's considered a 'cancellation' of the rejection.
       // However, the prompt implies "change mind" on REJECT leads to CROWN/REJECT buttons again.
       // Let's assume for now, this doesn't affect pumpkin counts directly, but the subsequent CROWN/REJECT will.
    }


    // Update the document status and updated timestamp
    const updatedData = await db.collection('PumpkinRequests').updateOne(
      { _id: objectId },
      { $set: { status: newStatus, updatedAt: new Date() } }
    );

    console.log(`Pumpkin request ${_id} status updated to ${newStatus}. Result:`, updatedData);
    return NextResponse.json({ acknowledged: updatedData.acknowledged, modifiedCount: updatedData.modifiedCount }, { status: 200 });

  } catch (error) {
    console.error('Error updating pumpkin request:', error);
    return NextResponse.json({ message: 'Internal server error', error: error.message }, { status: 500 });
  }
}

export async function DELETE(req) {
  const db = await getDb();
  const body = await req.json();
  const { _id } = body; // Expecting _id for deletion

  if (!_id) {
    return NextResponse.json({ message: 'Missing _id for DELETE request' }, { status: 400 });
  }

  try {
    const objectId = new ObjectId(_id);
    const pumpRequest = await db.collection("PumpkinRequests").findOne({ _id: objectId });

    if (!pumpRequest) {
      return NextResponse.json({ message: 'Pumpkin request not found for deletion' }, { status: 404 });
    }

    const { senderEmail, receiverEmail, status: currentStatus } = pumpRequest;

    // Decrement sender's pumpkin count if it was a pending pump being deleted
    if (currentStatus === "pending") {
      await db.collection("Users").updateOne(
        { email: senderEmail },
        { $inc: { pumpkins: -1 } }
      );
    } else if (currentStatus === "accepted") {
      // If an accepted pump is being deleted (unmatched via direct delete on backend), decrement both
      await db.collection("Users").updateOne(
        { email: senderEmail },
        { $inc: { pumpkins: -1 } }
      );
      await db.collection("Users").updateOne(
        { email: receiverEmail },
        { $inc: { pumpkins: -1 } }
      );
    }

    const deleteResult = await db.collection('PumpkinRequests').deleteOne({ _id: objectId });

    console.log("Delete result:", deleteResult);
    return NextResponse.json({ acknowledged: deleteResult.acknowledged, deletedCount: deleteResult.deletedCount }, { status: 200 });

  } catch (error) {
    console.error('Error deleting pumpkin request:', error);
    return NextResponse.json({ message: 'Internal server error', error: error.message }, { status: 500 });
  }
}
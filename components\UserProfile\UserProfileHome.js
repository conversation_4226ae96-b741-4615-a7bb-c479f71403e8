'use client';
import Image from 'next/image';
import React, { useEffect, useState, useCallback } from 'react';
import Button from '../UI/Button/Button';
import { useRouter } from 'next/navigation';
import { getCookie, setCookie } from 'cookies-next';
import { useCookies } from 'next-client-cookies';
import ImageInput from '../UI/ImageInput/ImageInput';
import TextInput from '../UI/Text Input/TextInput';
import { BiImageAdd, BiPlus, BiUser } from 'react-icons/bi';
import Loader from '../UI/Loader';
import HickieCountDisplay from '../UI/Button/HickieCountDisplay';

function UserProfileHome() {
  const cookies = useCookies();
  const router = useRouter();
  const [selectedUserData, setUserSelectedUserData] = useState(null);
  const [users, setUsers] = useState([]);
  const [errorMessage, setErrorMessage] = useState("");
  const [passwordErrorMessage, setPasswordErrorMessage] = useState("");
  const [isShooter, setIsShooter] = useState(true);
  const [showShooter, setShowShooter] = useState(true);
  const [setspecialName, setSetspecialName] = useState("");
  const [message, setMessage] = useState("");
  const [hint, setHint] = useState("");
  const [userPosts, setUserPosts] = useState([]);
  const [userNotifications, setUserNotifications] = useState([]);

  // NEW: States for match logic
  const [matchStatus, setMatchStatus] = useState('loading');
  const [pumpId, setPumpId] = useState(null);

  const senderEmail = cookies.get('email');
  const loggedInUserData = cookies.get('userData') ? JSON.parse(cookies.get('userData')) : null;
  const profileBeingViewedEmail = selectedUserData?.email;

  // --- Original fetchNotifications useEffect ---
  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        const response = await fetch("/api/pumpkin");
        const pumpkinData = await response.json();
        console.log("asambe", senderEmail);
        const combinedNotifications = [pumpkinData];
        console.log(combinedNotifications);
        const selectedNotifications = pumpkinData.filter(
          (post) => post.senderEmail === senderEmail
        );
        setUserNotifications(selectedNotifications);
      } catch (error) {
        console.error("Error fetching notifications:", error);
      }
    };
    fetchNotifications();
  }, [cookies, senderEmail]);

  useEffect(() => {
    console.log("asambe", userNotifications);
  }, [userNotifications]);
  // --- End Original fetchNotifications useEffect ---

  // --- Utility Functions ---
  function calculateAge(dateString) {
    const today = new Date();
    const birthDate = new Date(dateString);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  }

  function formatDate(dateString) {
    const months = [
      "January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December",
    ];
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, "0");
    const monthIndex = date.getMonth();
    const month = months[monthIndex];
    const year = date.getFullYear();
    return `${day} ${month} ${year}`;
  }
  const dateString = "1990-01-01";
  const formattedDate = formatDate(dateString);
  console.log(formattedDate);
  const dateOfBirth = "1990-01-01";
  const age = calculateAge(dateOfBirth);
  console.log(`Your age is ${age} years.`);
  // --- End Utility Functions ---

  // --- Shooter Modal Functions ---
  function toggleShooter() {
    setShowShooter(!showShooter);
  }

  function toggleIsShooter() {
    setIsShooter(!isShooter);
  }

  const [shooterRequest, setShooterRequest] = useState([]);

  function handleSubmit(e) {
    setShowShooter(true);
    if (e && e.preventDefault) {
      e.preventDefault();
    }
    const timestamp = new Date().toISOString();
    const senderEmail = cookies.get("email");
    const senderData = JSON.parse(cookies.get("userData"));
    const shooterRequestData = {
      specialName: setspecialName,
      hint: hint,
      message: message,
    };
    setShooterRequest(shooterRequestData);
    let res = fetch("/api/shooter", {
      method: "POST",
      body: JSON.stringify({
        senderEmail: senderEmail,
        senderData: senderData,
        email: selectedUserData.email,
        shooter: shooterRequestData,
        timestamp: timestamp,
        seen: false,
      }),
    }).then(async (response) => {
      const result = await response.json();
      alert('Shot sent successfully!');
      router.push("../../feed");
    }).catch(error => {
      console.error('Error sending shot:', error);
      alert('Error sending shot.');
    });
  }
  // --- End Shooter Modal Functions ---

  // --- Fetch User Data & Posts ---
  useEffect(() => {
    const _id = cookies.get("selectedUserProfile");
    if (!_id) {
      router.push("../../feed");
      return;
    }
    fetch("/api/auth")
      .then((res) => res.json())
      .then((data) => {
        setUsers(data);
        const selectedUser = data.find((user) => user.email === _id);
        setUserSelectedUserData(selectedUser);
      })
      .catch((error) => {
        console.error("Error fetching user data:", error);
      });
  }, [cookies, router]);

  useEffect(() => {
    if (!selectedUserData?.email) return;
    fetch("/api/post-image")
      .then((res) => res.json())
      .then((data) => {
        const selectedPosts = data.filter(
          (post) => post?.email === selectedUserData.email
        );
        setUserPosts(selectedPosts);
      })
      .catch((error) => {
        console.error("Error fetching post data:", error);
      });
  }, [selectedUserData]);
  // --- End Fetch User Data & Posts ---

  // --- NEW: Match Status Fetching ---
  const fetchMatchStatus = useCallback(async () => {
    if (!senderEmail || !profileBeingViewedEmail) {
      setMatchStatus('loading');
      return;
    }
    setMatchStatus('loading');
    setPumpId(null);

    try {
      const response = await fetch('/api/pumpkin');
      const pumps = await response.json();

      const sentPump = pumps.find(
        (pump) =>
          pump.senderEmail === senderEmail &&
          pump.receiverEmail === profileBeingViewedEmail
      );

      const receivedPump = pumps.find(
        (pump) =>
          pump.senderEmail === profileBeingViewedEmail &&
          pump.receiverEmail === senderEmail
      );

      if (sentPump) {
        setPumpId(sentPump._id);
        if (sentPump.status === 'pending') {
          setMatchStatus('sent_pending');
        } else if (sentPump.status === 'accepted') {
          setMatchStatus('accepted');
        } else if (sentPump.status === 'rejected') {
          setMatchStatus('rejected_by_them');
        } else if (sentPump.status === 'unmatched') {
          setMatchStatus('none');
        }
      } else if (receivedPump) {
        setPumpId(receivedPump._id);
        if (receivedPump.status === 'pending') {
          setMatchStatus('received_pending');
        } else if (receivedPump.status === 'accepted') {
          setMatchStatus('accepted');
        } else if (receivedPump.status === 'rejected') {
          setMatchStatus('rejected_by_me');
        } else if (receivedPump.status === 'unmatched') {
          setMatchStatus('none');
        }
      } else {
        setMatchStatus('none');
      }
    } catch (error) {
      console.error('Error fetching match status:', error);
      setMatchStatus('loading_error');
    }
  }, [senderEmail, profileBeingViewedEmail]);

  useEffect(() => {
    if (selectedUserData) {
      fetchMatchStatus();
    }
  }, [selectedUserData, fetchMatchStatus]);
  // --- END NEW: Match Status Fetching ---

  // --- NEW: Button Action Handlers ---
  const sendPump = async () => {
    if (!senderEmail || !profileBeingViewedEmail || !loggedInUserData) {
      alert("Please log in to send a Pump or data is missing.");
      return;
    }
    try {
      const response = await fetch('/api/pumpkin', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          senderEmail: senderEmail,
          senderData: loggedInUserData,
          email: profileBeingViewedEmail,
          timestamp: new Date().toISOString(),
          seen: false,
        }),
      });
      const result = await response.json();
      if (result.acknowledged && result.insertedId) {
        setPumpId(result.insertedId);
        setMatchStatus('sent_pending');
        alert('Pump request sent!');
      } else {
        alert(`Failed to send pump: ${result.message || JSON.stringify(result)}`);
      }
    } catch (error) {
      console.error('Error sending pump:', error);
      alert('Error sending pump request.');
    } finally {
      fetchMatchStatus();
    }
  };

  const cancelPump = async () => {
    if (!pumpId) {
      console.error("No pump ID to cancel.");
      return;
    }
    try {
      const response = await fetch('/api/pumpkin', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ _id: pumpId }),
      });
      const result = await response.json();
      if (result.acknowledged && result.deletedCount === 1) {
        setMatchStatus('none');
        setPumpId(null);
        alert('Pump request cancelled!');
      } else {
        alert(`Failed to cancel pump: ${result.message || JSON.stringify(result)}`);
      }
    } catch (error) {
      console.error('Error cancelling pump:', error);
      alert('Error cancelling pump request.');
    } finally {
      fetchMatchStatus();
    }
  };

  const updatePumpStatus = async (newStatus) => {
    if (!pumpId) {
      console.error("No pump ID to update.");
      return;
    }
    try {
      const response = await fetch('/api/pumpkin', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ _id: pumpId, status: newStatus }),
      });
      const result = await response.json();
      if (result.acknowledged && result.modifiedCount === 1) {
        alert(`Pump status updated to ${newStatus}!`);
      } else {
        alert(`Failed to update pump status: ${result.message || JSON.stringify(result)}`);
      }
    } catch (error) {
      console.error(`Error updating pump to ${newStatus}:`, error);
      alert(`Error updating pump status to ${newStatus}.`);
    } finally {
      fetchMatchStatus();
    }
  };

  const acceptPump = () => updatePumpStatus('accepted');
  const rejectPump = () => updatePumpStatus('rejected');
  const unmatchUsers = () => updatePumpStatus('unmatched');
  const changeMind = () => updatePumpStatus('pending');
  // --- END NEW: Button Action Handlers ---

  // --- Original `handleSubmitPumkin` and `handleSubmitDeletePumkin` (Kept as "junk code" as requested) ---
  function handleSubmitPumkin(e) {
    setShowShooter(true);
    e.preventDefault();
    const timestamp = new Date().toISOString();
    const senderEmail = cookies.get("email");
    const senderData = JSON.parse(cookies.get("userData"));
    let res = fetch("/api/pumpkin", {
      method: "POST",
      body: JSON.stringify({
        senderEmail: senderEmail,
        senderData: senderData,
        email: selectedUserData.email,
        timestamp: timestamp,
        seen: false,
      }),
    }).then(async (response) => {
      const result = await response.json();
      router.push("../../feed");
    });
  }

  function handleSubmitDeletePumkin(e) {
    setShowShooter(true);
    e.preventDefault();
    const timestamp = new Date().toISOString();
    const senderEmail = cookies.get("email");
    const senderData = JSON.parse(cookies.get("userData"));
    let res = fetch("/api/pumpkin", {
      method: "DELETE",
      body: JSON.stringify({
        email: selectedUserData.email,
      }),
    }).then(async (response) => {
      const result = await response.json();
      router.push("../../feed");
    });
  }
  // --- End original handlers ---

  console.log("selected user info", selectedUserData);

  // --- Button Rendering Logic (FINAL REVISION for Image 1 & 2) ---
  const renderButtons = () => {
    // If viewing own profile, show Edit Profile button
    if (selectedUserData && selectedUserData.email === senderEmail) {
      return (
        <div className="flex flex-col items-center space-y-2 w-full">
          <p className="font-Outfit font-bold text-sm lg:text-base text-gray-700 text-center">
            This is your profile!
          </p>
          <Button
            label={'Edit Profile'}
            variant={'primary'}
            onClick={() => router.push('/edit-profile')}
          />
        </div>
      );
    }

    // Show loading state
    if (matchStatus === 'loading') {
      return (
        <div className="flex flex-col items-center justify-center space-y-2 w-full">
          <Loader />
          <p className="font-Outfit font-bold text-xs text-gray-700 text-center">
            Checking match status...
          </p>
        </div>
      );
    }

    // Wrap the button groups in a single div here
    return (
      <div className="flex flex-col space-y-4 lg:flex-row lg:space-y-0 lg:gap-x-2 items-center w-full lg:w-auto"> {/* KEY: This wrapper will handle row/gap on desktop */}
        {/* Children below are the individual button groups */}

        {matchStatus === 'none' && (
          <>
          {/* PUMP!! button group */}
            <div className="flex flex-col items-center space-y-1 w-[150px] mx-auto lg:w-auto">
                <p className="font-Outfit font-bold text-xs text-gray-700 text-center">
                    Like what you see? Send A Match request!
                </p>
                <Button label={'PUMP!!'} variant={'gradient-black'} onClick={sendPump} className="w-full lg:w-[160px]" />
            </div>
            {/* Shoot your shot button group */}
            <div className="flex flex-col items-center space-y-1 w-[150px] mx-auto lg:w-auto">
                <p className="font-Outfit font-bold text-xs text-gray-700 text-center">
                    Send an anonymous message.
                </p>
                <Button
                    label={"Shoot your shot"}
                    variant={"secondary"} 
                    onClick={toggleShooter}
                    className="w-full lg:w-[160px]" // Fixed width for desktop
                />
            </div>
            
          </>
        )}

        {matchStatus === 'sent_pending' && (
          <>
            {/* GWABABA button group */}
            <div className="flex flex-col items-center space-y-1 w-[150px] mx-auto lg:w-auto">
                <p className="font-Outfit font-bold text-xs text-gray-700 text-center">
                    Getting cold feet? Click to unsend the request!
                </p>
                <Button label={'GWABABA'} variant={'gradient-orange'} onClick={cancelPump} className="w-full lg:w-[160px]" />
            </div>
            {/* Shoot your shot button group */}
            <div className="flex flex-col items-center space-y-1 w-[150px] mx-auto lg:w-auto">
                <p className="font-Outfit font-bold text-xs text-gray-700 text-center">
                    Send an anonymous message.
                </p>
                <Button
                    label={"Shoot your shot"}
                    variant={"secondary"}
                    onClick={toggleShooter}
                    className="w-full lg:w-[160px]"
                />
            </div>
          </>
        )}

        {matchStatus === 'received_pending' && (
          <>
            {/* CROWN button group */}
            <div className="flex flex-col items-center space-y-1 w-[150px] mx-auto lg:w-auto">
                <p className="font-Outfit font-bold text-xs text-gray-700 text-center">
                    Accept their match request.
                </p>
                <Button label={'CROWN'} variant={'gradient-orange'} onClick={acceptPump} className="w-full lg:w-[160px]" />
            </div>
            {/* REJECT button group */}
            <div className="flex flex-col items-center space-y-1 w-[150px] mx-auto lg:w-auto">
                <p className="font-Outfit font-bold text-xs text-gray-700 text-center">
                    Not your type? Click to reject.
                </p>
                <Button label={'REJECT'} variant={'primary'} onClick={rejectPump} className="w-full lg:w-[160px]" />
            </div>
          </>
        )}

        {matchStatus === 'accepted' && (
          <>
            {/* UNMATCH button group */}
            <div className="flex flex-col items-center space-y-1 w-[150px] mx-auto lg:w-auto">
                <p className="font-Outfit font-bold text-xs text-gray-700 text-center">
                    You don't want your Pumpkin no more? Click to unmatch.
                </p>
                <Button label={'UNMATCH'} variant={'primary'} onClick={unmatchUsers} className="w-full lg:w-[160px]" />
            </div>
            {/* Shoot your shot button group */}
            <div className="flex flex-col items-center space-y-1 w-[150px] mx-auto lg:w-auto">
                <p className="font-Outfit font-bold text-xs text-gray-700 text-center">
                    Send an anonymous message.
                </p>
                <Button
                    label={"Shoot your shot"}
                    variant={"seconday"}
                    onClick={toggleShooter}
                    className="w-full lg:w-[160px]"
                />
            </div>
          </>
        )}

        {matchStatus === 'rejected_by_them' && (
          <>
            {/* ASKIES! button group */}
            <div className="flex flex-col items-center space-y-1 w-[150px] mx-auto lg:w-auto">
                <p className="font-Outfit font-bold text-xs text-gray-700 text-center">
                    This person has declined your request.
                </p>
                <Button label={'ASKIES!'} variant={'tertiary'} onClick={() => {}} disabled={true} className="w-full lg:w-[160px]" />
            </div>
            {/* Shoot your shot button group */}
            <div className="flex flex-col items-center space-y-1 w-[150px] mx-auto lg:w-auto">
                <p className="font-Outfit font-bold text-xs text-gray-700 text-center">
                    Send an anonymous message.
                </p>
                <Button
                    label={"Shoot your shot"}
                    variant={"secondary"}
                    onClick={toggleShooter}
                    className="w-full lg:w-[160px]"
                />
            </div>
          </>
        )}

        {matchStatus === 'rejected_by_me' && (
          <>
            {/* CHANGE MIND button group */}
            <div className="flex flex-col items-center space-y-1 w-[150px] mx-auto lg:w-auto">
                <p className="font-Outfit font-bold text-xs text-gray-700 text-center">
                    Made a mistake? It's okay to change your mind.
                </p>
                <Button label={'CHANGE MIND'} variant={'gradient-orange'} onClick={changeMind} className="w-full lg:w-[160px]" />
            </div>
            {/* Shoot your shot button group */}
            <div className="flex flex-col items-center space-y-1 w-[150px] mx-auto lg:w-auto">
                <p className="font-Outfit font-bold text-xs text-gray-700 text-center">
                    Send an anonymous message.
                </p>
                <Button
                    label={"Shoot your shot"}
                    variant={"secondary"}
                    onClick={toggleShooter}
                    className="w-full lg:w-[160px]"
                />
            </div>
          </>
        )}

        {matchStatus === 'loading_error' && (
          <div className="flex flex-col items-center justify-center space-y-2 w-full text-red-500 font-Outfit text-center">
            Error loading match status. Please refresh.
          </div>
        )}

        {/* Default case handled by initial if block */}
      </div>
    );
  };

  // --- End Button Rendering Logic ---

  if (!selectedUserData) {
    return (
      <div className="w-full flex items-center justify-center h-96 text-xl">
        <Loader />
      </div>
    );
  }

  return (
    <div className=" relative bg-gradient-to-b space-y-6 from-black/5 to-white/0 lg:py-16 py-6 px-8 lg:px-16">
      <div className={`${showShooter ? "hidden" : "block"}`}>
        {isShooter ? (
          <div className="fixed z-50 text-black bg-black/20 h-screen justify-center  w-screen flex flex-col py-16 px-6 l space-y-10  items-center left-0 top-0    rounded-3xl">
            <div className=" text-black flex flex-col lg:py-16 py-8 px-6 l space-y-10  items-center max-w-[88vw] lg:max-w-[40vw]    bg-[#ffffff] rounded-3xl">
              <div className=" flex flex-col items-center text-lg font-semibold">
                <p>Heey, {selectedUserData.name}</p>
                <p className="text-center">
                  Welcome to Pumpkins Shot Shooter
                </p>
              </div>
              <div className="text-justify">
                <p>
                  Shot Shooter lets you hit your crush up without having to go
                  through the gwaabs, aka - gwababa, coz we've got you. This
                  happens anonymously, and they never get to know its you.
                  Take the chance, send them a sweet text.{" "}
                </p>
              </div>
              <div className="flex flex-col items-center space-y-2">
                <button
                  onClick={toggleIsShooter}
                  className="bg-black px-24 text-white rounded-full font-bold py-4"
                >
                  <p>Shoot</p>
                </button>
                <p
                  className=" text-white bg-black px-24 rounded-full font-bold py-4 hover:underline cursor-pointer"
                  onClick={toggleShooter}
                >
                  Cancel
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="fixed z-50 text-white bg-black/20 h-screen justify-center  w-screen flex flex-col py-16 px-6 l space-y-10  items-center left-0 top-0 rounded-3xl">
            <div className=" text-black flex flex-col lg:py-16 py-8 px-6 l space-y-10  items-center max-w-[88vw] lg:max-w-[40vw]    bg-[#ffffff] rounded-3xl">
              <div className="w-full space-y-2">
                <p className="text-sm">
                  Shoot your shot player, Whats your message to them?
                </p>
                <textarea
                  type="text"
                  className="w-full py-2 px-4 rounded-xl text-black bg-[#cfcece]"
                  value={message}
                  placeholder="Shoot Your Shot"
                  onChange={(e) => setMessage(e.target.value)}
                />
              </div>
              <div className="flex flex-col items-center space-y-2">
                <button
                  onClick={handleSubmit}
                  className="bg-black px-24 text-white rounded-full font-bold py-4"
                >
                  <p>Shoot</p>
                </button>
                <p
                  className="hover:underline cursor-pointer text-white bg-black px-24 rounded-full font-bold py-4"
                  onClick={() => {
                    toggleShooter();
                    toggleIsShooter();
                  }}
                >
                  Cancel
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
      <div className="flex flex-col lg:flex-row items-center lg:space-y-0 space-y-6 lg:space-x-12">
        <div>
          {selectedUserData.profilePicture ? (
            <div className="relative">
              <Image
                src={selectedUserData.profilePicture}
                width={100}
                height={100}
                className="rounded-full w-[100px] h-[100px] object-cover border-2 border-white shadow-md"
                alt="profile"
                unoptimized={true}
                priority={true}
                loader={({ src }) => src}
                onError={(e) => {
                  console.error('Error loading profile image:', e);
                  e.target.src = 'https://via.placeholder.com/100?text=Profile';
                }}
              />
            </div>
          ) : (
            <div className="bg-gray-300 flex items-center justify-center h-[100px] w-[100px] rounded-full p-6 border-2 border-white shadow-md">
              <BiUser className="text-5xl" />
            </div>
          )}
        </div>
        <div className="flex flex-col items-center lg:items-start space-y-2">
          <div className="flex font-bold text-sm space-x-2 lg:space-x-4 lg:text-xl">
            <p className="">{selectedUserData.name}</p>
            <p className="">{selectedUserData.surname},</p>
            <p>{calculateAge(selectedUserData.dob)}</p>
          </div>
          <div className="flex flex-col lg:flex-row lg:space-y-0 space-x-0 lg: lg:space-x-2 items-center justify-center  text-base  ">
            <p className="text-gray-600">@{selectedUserData.username}</p>
            {selectedUserData.dob ? (
              <p className="text-gray-600">{selectedUserData.dob}</p>
            ) : null}
          </div>
        </div>
      </div>
      {/* This is the main container for the stats and buttons */}
      <div className="flex space-y-4 lg:space-y-0  flex-col lg:flex-row items-center lg:justify-between w-full">
        {/* Stats section (Posts, Pumpkins, Hickies) */}
        <div className="flex items-center space-x-6">
          <div className="flex  space-x-2 lg:text-lg text-sm">
            <p className="font-bold">{selectedUserData.posts?.length || 0}</p>
            <p>Posts</p>
          </div>
          <div className="flex  space-x-2 lg:text-lg text-sm">
            <p className="font-bold">{selectedUserData.pumpkins || 0}</p>
            <p>Pumpkins</p>
          </div>
          <div className="flex  space-x-2 lg:text-lg text-sm">
            <p className="font-bold">{selectedUserData.hickies || 0}</p>
            <p>Hickies</p>
          </div>
        </div>

        {/* Button container - Controls alignment and spacing of button groups */}
        {/* On mobile: flex-col (stacks), items-center (centers children horizontally), space-y-4 (vertical space between button groups) */}
        {/* On desktop: lg:flex-row (row), lg:space-x-4 (horizontal space), lg:justify-end (pushes to right), items-center (vertical alignment) */}
        <div className="flex flex-col space-y-4 lg:space-y-0 lg:flex-row lg:gap-x-2 font-bold text-xl lg:text-xl items-center w-full lg:justify-end"> {/* KEY: Added lg:justify-end and kept lg:gap-x-2 */}
          {renderButtons()}
        </div>
      </div>

      <div className=" grid grid-rows-2 lg:grid-cols-2 lg:grid-flow-row grid-flow-col">
        <div className="space-y-12">
          <div className="space-y-4">
            <div className="font-bold ">
              <p>Bio</p>
            </div>
            <div className=" max-w-[400px] lg:text-xl">
              <p>{selectedUserData.bio}</p>
            </div>
          </div>
          <div className="space-y-4">
            <div className="font-bold ">
              <p>Hobbies</p>
            </div>
            <div className="flex space-x-2 max-w-[90vw] overflow-auto py-2">
              {selectedUserData.hobbies?.map((hobbie, index) => (
                <div key={index} className="flex">
                  <p
                    key={index}
                    className="bg-[#D9D9D9] flex-wrap px-4 py-1 rounded-full font-semibold"
                  >
                    {hobbie}
                  </p>
                </div>
              ))}
            </div>
          </div>
          <div className="space-y-4">
            <div className="font-bold ">
              <p>Passions</p>
            </div>
            <div className=" flex space-x-2 sm:w-[90vw] overflow-auto  py-2">
              {selectedUserData.passions?.map((passion, index) => (
                <div key={index} className="flex ">
                  <p
                    key={index}
                    className="bg-[#D9D9D9] text-nowrap flex-wrap px-4 py-1 rounded-full font-semibold"
                  >
                    {passion}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
        <div className="space-y-6 pt-6 lg:pt-0">
          <div className="font-bold text-xl">
            <p>Posts</p>
          </div>
          <div className="grid grid-cols-3 gap-3">
            {userPosts.length > 0 ? (
              userPosts.map((post, index) => (
                <Image
                  key={index}
                  id={post._id}
                  src={post.imagePost}
                  width={200}
                  height={200}
                  className="object-cover w-full h-auto aspect-square rounded-md shadow-sm hover:shadow-md transition-shadow"
                  alt={`Post ${index + 1}`}
                  unoptimized={true}
                  loader={({ src }) => src}
                  onError={(e) => {
                    console.error("Error loading post image:", e);
                    e.target.src = "https://via.placeholder.com/200?text=Post";
                  }}
                />
              ))
            ) : (
              <div className="col-span-3 text-center py-8 text-gray-500">
                No posts yet
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default UserProfileHome;
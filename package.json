{"name": "pumkin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@roq/nextjs": "^2.0.3", "@uploadthing/react": "^6.7.2", "cloudinary-react": "^1.8.1", "cookies-next": "^4.2.1", "firebase": "^10.12.4", "firebase-admin": "^13.4.0", "mongodb": "^6.8.0", "next": "14.2.4", "next-client-cookies": "^1.1.1", "next-cloudinary": "^6.6.2", "next-cookies": "^2.0.3", "nprogress": "^0.2.0", "react": "^18", "react-dom": "^18", "react-icons": "^5.2.1", "react-image-crop": "^11.0.6", "react-multi-carousel": "^2.8.5", "react-responsive-carousel": "^3.2.23", "uploadthing": "^6.13.2"}, "devDependencies": {"@roq/cli": "^0.0.17", "@types/react": "18.3.3", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "tailwindcss": "^3.4.1"}}
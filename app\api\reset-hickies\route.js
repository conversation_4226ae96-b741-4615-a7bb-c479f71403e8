import clientPromise from "@/lib/mongodb";

export async function POST(req) {
  try {
    const client = await clientPromise;
    const db = client.db("Pumpkin");
    
    // Reset hickies to 0 in both collections
    const usersResult = await db.collection("Users").updateMany(
      {},
      { $set: { hickies: 0, hickiedBy: [] } }
    );

    const usersLowerResult = await db.collection("users").updateMany(
      {},
      { $set: { hickies: 0, hickiedBy: [] } }
    );

    return new Response(JSON.stringify({
      success: true,
      message: "Hickie counts reset successfully",
      usersUpdated: usersResult.modifiedCount,
      usersLowerUpdated: usersLowerResult.modifiedCount
    }), { status: 200 });
  } catch (error) {
    console.error("Error resetting hickies:", error);
    return new Response(JSON.stringify({ error: "Internal Server Error" }), { status: 500 });
  }
} 
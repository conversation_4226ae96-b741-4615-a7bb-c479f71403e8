/** @type {import('tailwindcss').Config} */

module.exports = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
   borderRadius: {
        '4xl': '2rem', // You define what '4xl' means, e.g., 32px (1rem = 16px)
        '5xl': '3rem', // e.g., 48px
        '6xl': '4rem', // e.g., 64px
      }
    },
  },
  plugins: [],
}


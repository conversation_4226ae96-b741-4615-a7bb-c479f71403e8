'use client';
import { useState } from 'react';

export default function ResetHickiesPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState(null);

  const handleReset = async () => {
    if (!confirm('Are you sure you want to reset all hickie counts to 0? This action cannot be undone.')) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/reset-hickies', {
        method: 'POST',
      });
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ error: 'Failed to reset hickies' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
        <h1 className="text-2xl font-bold mb-6 text-center">Reset Hickie Counts</h1>
        
        <p className="text-gray-600 mb-6 text-center">
          This will reset all hickie counts to 0 and clear all hickiedBy arrays. 
          The new universal hickie count system will take over from here.
        </p>

        <button
          onClick={handleReset}
          disabled={isLoading}
          className="w-full bg-red-500 hover:bg-red-600 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded transition-colors"
        >
          {isLoading ? 'Resetting...' : 'Reset All Hickie Counts'}
        </button>

        {result && (
          <div className={`mt-4 p-4 rounded ${result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            <p className="font-bold">{result.success ? 'Success!' : 'Error!'}</p>
            <p>{result.message || result.error}</p>
            {result.usersUpdated !== undefined && (
              <p>Users collection updated: {result.usersUpdated}</p>
            )}
            {result.usersLowerUpdated !== undefined && (
              <p>users collection updated: {result.usersLowerUpdated}</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
} 
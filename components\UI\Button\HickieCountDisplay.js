"use client";

import React from "react";
import { useHickieCount } from "./useHickieCount";

// Component for displaying hickie count using the universal source of truth
// This component doesn't provide interaction, just displays the current count
export default function HickieCountDisplay({ targetEmail, initialHickies = 0, initialHickiedBy = [] }) {
  const { hickieCount } = useHickieCount(targetEmail, initialHickies, initialHickiedBy);

  return (
    <span className="font-bold">{hickieCount}</span>
  );
} 
import { initializeApp, cert, getApps } from "firebase-admin/app";
import { getFirestore } from "firebase-admin/firestore";
import { db } from "@/lib/firebaseAdmin";

// Get service account credentials from environment variable
let serviceAccount;
try {
  // For Vercel/production: expects a stringified JSON in FIREBASE_SERVICE_ACCOUNT_KEY
  serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
} catch (e) {
  // Optional: fallback for local dev if you want to use a file
  // serviceAccount = require("../path/to/serviceAccountKey.json");
  throw new Error(
    "FIREBASE_SERVICE_ACCOUNT_KEY env variable is missing or invalid."
  );
}

// Initialize only if not already initialized
const app =
  getApps().length === 0
    ? initializeApp({
        credential: cert(serviceAccount),
      })
    : getApps()[0];

const db = getFirestore(app);

export { db };
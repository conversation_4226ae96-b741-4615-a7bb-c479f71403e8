'use client';
import React, { useEffect, useState, useCallback, useRef } from 'react';
import ProfileCardMatches from '../UI/Card/ProfileCardMatches';
import Carousel from 'react-multi-carousel';
import 'react-multi-carousel/lib/styles.css';
import { useCookies } from 'next-client-cookies';
import CircleIconButton from '../UI/Button/CircleIconButton';
import Loader from '../UI/Loader';
import { BiPlus } from 'react-icons/bi';
import ProfileHickieButton from "@/components/UI/Button/ProfileHickieButton";

function MatchesHome() {
  const cookies = useCookies();
  const [users, setUsers] = useState([]);
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  const [carouselKey, setCarouselKey] = useState(0);
  const carouselRef = useRef(null);
  const [isPumped, setIsPumped] = useState(false);
  const [pumpId, setPumpId] = useState(null);

  const senderEmail = cookies.get('email');
  const loggedInUserData = cookies.get('userData') ? JSON.parse(cookies.get('userData')) : null;
  const currentViewedUser = users[currentSlideIndex];

  const fetchAllUsers = useCallback(async () => {
    try {
      const res = await fetch('/api/auth');
      const data = await res.json();
      if (Array.isArray(data)) {
        const validUsers = data.filter(user => user && user.email);
        setUsers(validUsers);
      } else {
        setUsers([]);
      }
    } catch (error) {
      setUsers([]);
    }
  }, []);

  useEffect(() => {
    fetchAllUsers();
  }, [fetchAllUsers]);

  const handleHickieUpdate = useCallback((targetEmail, newHickieCount, newHickiedBy) => {
    setUsers(prevUsers => 
      prevUsers.map(user => 
        user.email === targetEmail 
          ? { ...user, hickies: newHickieCount, hickiedBy: newHickiedBy }
          : user
      )
    );
    setCarouselKey(prevKey => prevKey + 1);
  }, []);

  const fetchPumpStatus = useCallback(async () => {
    if (!senderEmail || !currentViewedUser?.email) {
      setIsPumped(false);
      setPumpId(null);
      return;
    }
    try {
      const pumpResponse = await fetch(`/api/pumpkin?senderEmail=${senderEmail}&receiverEmail=${currentViewedUser.email}`);
      const pumps = await pumpResponse.json();
      const sentPump = pumps.find(
        (pump) =>
          pump.senderEmail === senderEmail &&
          pump.receiverEmail === currentViewedUser.email
      );
      const receivedPump = pumps.find(
        (pump) =>
          pump.senderEmail === currentViewedUser.email &&
          pump.receiverEmail === senderEmail
      );
      if (sentPump && (sentPump.status === 'pending' || sentPump.status === 'accepted')) {
        setIsPumped(true);
        setPumpId(sentPump._id);
      } else if (receivedPump && (receivedPump.status === 'pending' || receivedPump.status === 'accepted')) {
        setIsPumped(true);
        setPumpId(receivedPump._id);
      } else {
        setIsPumped(false);
        setPumpId(null);
      }
    } catch (error) {
      setIsPumped(false);
      setPumpId(null);
    }
  }, [senderEmail, currentViewedUser]);

  useEffect(() => {
    if (currentViewedUser) {
      fetchPumpStatus();
    }
  }, [currentViewedUser, fetchPumpStatus]);

  const handlePump = async () => {
    if (!senderEmail || !currentViewedUser?.email || !loggedInUserData) {
      alert("Please log in or select a profile to send a Pump.");
      return;
    }
    const wasPumped = isPumped;
    setIsPumped(prev => !prev);
    try {
      if (wasPumped && pumpId) {
        const response = await fetch('/api/pumpkin', {
          method: 'DELETE',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ _id: pumpId }),
        });
        const result = await response.json();
        if (!result.acknowledged || result.deletedCount !== 1) {
          setIsPumped(prev => !prev);
        }
      } else {
        const response = await fetch('/api/pumpkin', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            senderEmail: senderEmail,
            senderData: loggedInUserData,
            email: currentViewedUser.email,
            timestamp: new Date().toISOString(),
            seen: false,
            status: 'pending',
          }),
        });
        const result = await response.json();
        if (!result.acknowledged || !result.insertedId) {
          setIsPumped(prev => !prev);
        }
      }
      await fetchPumpStatus();
    } catch (error) {
      setIsPumped(prev => !prev);
    }
  };

  const responsive = {
    superLargeDesktop: { breakpoint: { max: 4000, min: 3000 }, items: 1 },
    desktop: { breakpoint: { max: 3000, min: 1024 }, items: 1 },
    tablet: { breakpoint: { max: 1024, min: 464 }, items: 1 },
    mobile: { breakpoint: { max: 464, min: 0 }, items: 1 },
  };

  if (!currentViewedUser) {
    return (
      <div className="min-h-screen w-full flex items-center justify-center">
        <Loader />
        <p className="ml-2">Loading matches...</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen w-full">
      <div className="w-full h-auto max-w-[29.4rem] sm:max-w-[16.4rem] md:max-w-[14.1rem] lg:max-w-[19.8rem] xl:max-w-[19.8rem] mx-auto px-4 pt-8 overflow-x-hidden overflow-y-hidden">
        <p className="text-xs font-bold text-black" style={{ visibility: "hidden" }}>.</p>
        <Carousel
          swipeable={true}
          draggable={true}
          showDots={false}
          infinite={true}
          responsive={responsive}
          containerClass="matches-carousel-container"
          itemClass="px-5"
          ref={carouselRef}
          beforeChange={(nextSlide) => {
            if (users.length > 0) {
              const newIndex = ((nextSlide % users.length) + users.length) % users.length;
              setCurrentSlideIndex(newIndex);
            } else {
              setCurrentSlideIndex(nextSlide);
            }
          }}
        >
          {users.map((user) => (
            <div key={`${user._id}-${user.email}`} className="w-full flex justify-center pb-8">
              <ProfileCardMatches
                id={user._id}
                image={user.profilePicture}
                username={user.username}
                name={user.name}
                surname={user.surname}
                dob={user.dob}
                pumpkins={user.pumpkins}
                email={user.email}
                coverPicture={user.coverPicture}
                city={user.city || ""}
                country={user.country || ""}
                hickies={user.hickies || 0}
                hickiedBy={user.hickiedBy || []}
                onHickieUpdate={handleHickieUpdate}
              />
            </div>
          ))}
        </Carousel>
      </div>
      <div className="flex flex-row gap-x-8 items-center justify-center w-full max-w-xs sm:max-w-md mx-auto pt-4 pb-6 flex-shrink-0">
        <div className="flex flex-col items-center space-y-1">
          <p className="font-Outfit font-bold text-xs text-gray-700 text-center uppercase">
            Like
          </p>
          <div className="flex items-center space-x-3">
            <p className="text-base lg:text-lg font-bold text-gray-700">{currentViewedUser.hickies || 0}</p>
            <ProfileHickieButton
              targetEmail={currentViewedUser.email}
              initialHickies={currentViewedUser.hickies || 0}
              initialHickiedBy={currentViewedUser.hickiedBy || []}
              onHickieUpdate={handleHickieUpdate}
            />
          </div>
          <p className="font-Outfit font-bold text-xs text-gray-700 text-center uppercase mt-1">
            HICKIE
          </p>
        </div>
        <div className="flex flex-col items-center space-y-1">
          <p className="font-Outfit font-bold text-xs text-gray-700 text-center uppercase">
            Follow
          </p>
          <CircleIconButton
            onClick={handlePump}
            icon={BiPlus}
            isPumped={isPumped}
          />
          <p className="font-Outfit font-bold text-xs text-gray-700 text-center uppercase mt-1">
            PUMP
          </p>
        </div>
      </div>
    </div>
  );
}

export default MatchesHome;